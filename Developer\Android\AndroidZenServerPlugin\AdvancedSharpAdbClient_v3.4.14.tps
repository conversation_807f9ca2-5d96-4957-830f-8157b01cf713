<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>AdvancedSharpAdbClient</Name>
  <!-- Software Name and Version  -->
<!-- Software Name: AdvancedSharpAdbClient
    Download Link: https://github.com/SharpAdb/AdvancedSharpAdbClient/releases/tag/v3.4.14
    Version: 3.4.14
    Notes: It will be used in various C# automation in UE5 to communicate with android phones, including: android file tool, zen server, UAT.
        -->
<Location>//Fortnite/Main</Location>
<Function>Library allows easier communication from C# with connected development android phones.</Function>
<Eula>https://github.com/SharpAdb/AdvancedSharpAdbClient/blob/v3.4.14/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensee</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>